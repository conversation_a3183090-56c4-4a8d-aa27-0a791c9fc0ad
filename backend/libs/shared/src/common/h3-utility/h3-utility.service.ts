import { Injectable, BadRequestException } from '@nestjs/common';
import { polygonToCells } from 'h3-js';

export interface GeoJsonPolygon {
  type: 'Polygon';
  coordinates: number[][][];
}

export interface LatLng {
  lat: number;
  lng: number;
}

@Injectable()
export class H3UtilityService {
  /**
   * Convert a GeoJSON polygon to H3 indexes at resolution 8
   */
  polygonToH3Indexes(polygon: GeoJsonPolygon, resolution = 8): bigint[] {
    try {
      this.validateGeoJsonPolygon(polygon);

      // Convert coordinates to the format expected by h3-js
      const h3Polygon = this.convertGeoJsonToH3Format(polygon);
      console.log('h3Polygon', h3Polygon);
      // Get H3 indexes as strings
      const h3IndexStrings = polygonToCells(h3Polygon, resolution);
      console.log('h3Polygon', h3IndexStrings);
      // Convert strings to BigInt
      return h3IndexStrings.map(indexStr => BigInt(`0x${indexStr}`));
    } catch (error: any) {
      throw new BadRequestException(
        `Failed to convert polygon to H3 indexes: ${error?.message || 'Unknown error'}`,
      );
    }
  }

  /**
   * Convert array of lat/lng coordinates to GeoJSON polygon
   */
  coordinatesToGeoJsonPolygon(coordinates: LatLng[]): GeoJsonPolygon {
    if (!coordinates || coordinates.length < 3) {
      throw new BadRequestException(
        'Polygon must have at least 3 coordinates',
      );
    }

    // Ensure the polygon is closed (first and last coordinates are the same)
    const coords = [...coordinates];
    if (
      coords[0].lat !== coords[coords.length - 1].lat ||
      coords[0].lng !== coords[coords.length - 1].lng
    ) {
      coords.push(coords[0]);
    }

    // Convert to GeoJSON format [lng, lat]
    const geoJsonCoords = coords.map(coord => [coord.lng, coord.lat]);

    return {
      type: 'Polygon',
      coordinates: [geoJsonCoords],
    };
  }

  /**
   * Validate GeoJSON polygon structure
   */
  private validateGeoJsonPolygon(polygon: any): void {
    if (!polygon || polygon.type !== 'Polygon') {
      throw new BadRequestException('Invalid polygon: must be a GeoJSON Polygon');
    }

    if (!Array.isArray(polygon.coordinates) || polygon.coordinates.length === 0) {
      throw new BadRequestException('Invalid polygon: coordinates must be a non-empty array');
    }

    const exteriorRing = polygon.coordinates[0];
    if (!Array.isArray(exteriorRing) || exteriorRing.length < 4) {
      throw new BadRequestException('Invalid polygon: exterior ring must have at least 4 coordinates');
    }

    // Check if polygon is closed
    const first = exteriorRing[0];
    const last = exteriorRing[exteriorRing.length - 1];
    if (first[0] !== last[0] || first[1] !== last[1]) {
      throw new BadRequestException('Invalid polygon: exterior ring must be closed');
    }

    // Validate coordinate format
    for (const ring of polygon.coordinates) {
      for (const coord of ring) {
        if (!Array.isArray(coord) || coord.length !== 2) {
          throw new BadRequestException('Invalid polygon: coordinates must be [lng, lat] pairs');
        }

        const [lng, lat] = coord;
        if (typeof lng !== 'number' || typeof lat !== 'number') {
          throw new BadRequestException('Invalid polygon: coordinates must be numbers');
        }

        if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
          throw new BadRequestException('Invalid polygon: coordinates out of valid range');
        }
      }
    }
  }

  /**
   * Convert GeoJSON polygon to H3-js format
   */
  private convertGeoJsonToH3Format(polygon: GeoJsonPolygon): number[][][] {
    return polygon.coordinates.map(ring =>
      ring.map(coord => [coord[1], coord[0]]) // Convert [lng, lat] to [lat, lng]
    );
  }

  /**
   * Convert H3 index from BigInt to string format
   */
  h3IndexToString(h3Index: bigint): string {
    return h3Index.toString(16);
  }

  /**
   * Convert H3 index from string to BigInt format
   */
  stringToH3Index(h3IndexString: string): bigint {
    return BigInt(`0x${h3IndexString}`);
  }

  /**
   * Validate array of coordinates
   */
  validateCoordinates(coordinates: LatLng[]): void {
    if (!Array.isArray(coordinates)) {
      throw new BadRequestException('Coordinates must be an array');
    }

    if (coordinates.length < 3) {
      throw new BadRequestException('Polygon must have at least 3 coordinates');
    }

    for (const coord of coordinates) {
      if (!coord || typeof coord.lat !== 'number' || typeof coord.lng !== 'number') {
        throw new BadRequestException('Each coordinate must have numeric lat and lng properties');
      }

      if (coord.lng < -180 || coord.lng > 180 || coord.lat < -90 || coord.lat > 90) {
        throw new BadRequestException('Coordinates out of valid range');
      }
    }
  }
}
