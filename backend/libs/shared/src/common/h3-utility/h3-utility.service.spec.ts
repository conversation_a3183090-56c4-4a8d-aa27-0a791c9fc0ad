import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { H3UtilityService } from './h3-utility.service';

describe('H3UtilityService', () => {
  let service: H3UtilityService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [H3UtilityService],
    }).compile();

    service = module.get<H3UtilityService>(H3UtilityService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('coordinatesToGeoJsonPolygon', () => {
    it('should convert coordinates to GeoJSON polygon', () => {
      const coordinates = [
        { lat: 9.9312, lng: 76.2673 },
        { lat: 9.9412, lng: 76.2773 },
        { lat: 9.9512, lng: 76.2873 },
      ];

      const result = service.coordinatesToGeoJsonPolygon(coordinates);

      expect(result.type).toBe('Polygon');
      expect(result.coordinates).toHaveLength(1);
      expect(result.coordinates[0]).toHaveLength(4);
      expect(result.coordinates[0][0]).toEqual([76.2673, 9.9312]);
      expect(result.coordinates[0][3]).toEqual([76.2673, 9.9312]);
    });

    it('should throw error for insufficient coordinates', () => {
      const coordinates = [
        { lat: 9.9312, lng: 76.2673 },
        { lat: 9.9412, lng: 76.2773 },
      ];

      expect(() => service.coordinatesToGeoJsonPolygon(coordinates)).toThrow(
        BadRequestException,
      );
    });
  });

  describe('validateCoordinates', () => {
    it('should validate correct coordinates', () => {
      const coordinates = [
        { lat: 9.9312, lng: 76.2673 },
        { lat: 9.9412, lng: 76.2773 },
        { lat: 9.9512, lng: 76.2873 },
      ];

      expect(() => service.validateCoordinates(coordinates)).not.toThrow();
    });

    it('should throw error for invalid coordinates', () => {
      const coordinates = [
        { lat: 91, lng: 76.2673 }, // Invalid latitude
        { lat: 9.9412, lng: 76.2773 },
        { lat: 9.9512, lng: 76.2873 },
      ];

      expect(() => service.validateCoordinates(coordinates)).toThrow(
        BadRequestException,
      );
    });
  });

  describe('polygonToH3Indexes', () => {
    it('should convert polygon to H3 indexes', () => {
      const polygon = {
        type: 'Polygon' as const,
        coordinates: [
          [
            [76.2673, 9.9312],
            [76.2773, 9.9412],
            [76.2873, 9.9512],
            [76.2673, 9.9312],
          ],
        ],
      };

      const result = service.polygonToH3Indexes(polygon, 8);

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(typeof result[0]).toBe('bigint');
    });
  });
});
