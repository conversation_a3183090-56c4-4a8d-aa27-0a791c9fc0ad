'use client';
import * as React from 'react';
import { Car, LayoutDashboard } from 'lucide-react';

import { NavMain } from '@/components/nav-main';
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from '@/components/ui/sidebar';
import { LogoName } from './logo-name';

// Driver management data
const data = {
   user: {
      name: 'john doe',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
   },
   navMain: [
      {
         title: 'Dashboard',
         url: '/dashboard/home',
         icon: LayoutDashboard,
         isActive: false,
      },
      {
         title: 'Drivers',
         url: '/dashboard/drivers',
         icon: Car,
         isActive: true,
      },
   ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   return (
      <Sidebar className='bg-white border-r border-gray-200' collapsible='icon' {...props}>
         <SidebarHeader className='bg-white border-b border-gray-200 p-3'>
            <div className='flex items-center justify-center'>
               <LogoName width={60} height={60} />
            </div>
         </SidebarHeader>
         <SidebarContent className='bg-white p-2'>
            <NavMain items={data.navMain} />
         </SidebarContent>
         <SidebarRail />
      </Sidebar>
   );
}
