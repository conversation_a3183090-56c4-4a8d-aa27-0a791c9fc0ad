-- AlterTable
ALTER TABLE "cities" ADD COLUMN     "h3_indexes" BIGINT[],
ADD COLUMN     "polygon" JSONB;

-- CreateTable
CREATE TABLE "h3_index_to_zones" (
    "id" UUID NOT NULL,
    "h3_index" BIGINT NOT NULL,
    "zone_id" UUID NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "h3_index_to_zones_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "zones" (
    "id" UUID NOT NULL,
    "name" TEXT NOT NULL,
    "is_city" BOOLEAN NOT NULL DEFAULT false,
    "polygon" JSONB NOT NULL,
    "h3_indexes" BIGINT[],
    "city_id" UUID,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ,

    CONSTRAINT "zones_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "idx_h3_index_to_zone_h3_index" ON "h3_index_to_zones"("h3_index");

-- CreateIndex
CREATE INDEX "idx_h3_index_to_zone_zone_id" ON "h3_index_to_zones"("zone_id");

-- CreateIndex
CREATE UNIQUE INDEX "h3_index_to_zones_h3_index_zone_id_key" ON "h3_index_to_zones"("h3_index", "zone_id");

-- CreateIndex
CREATE INDEX "idx_zone_name" ON "zones"("name");

-- CreateIndex
CREATE INDEX "idx_zone_is_city" ON "zones"("is_city");

-- CreateIndex
CREATE INDEX "idx_zone_city_id" ON "zones"("city_id");

-- CreateIndex
CREATE INDEX "idx_zone_h3_indexes" ON "zones"("h3_indexes");

-- CreateIndex
CREATE INDEX "idx_city_h3_indexes" ON "cities"("h3_indexes");

-- AddForeignKey
ALTER TABLE "h3_index_to_zones" ADD CONSTRAINT "h3_index_to_zones_zone_id_fkey" FOREIGN KEY ("zone_id") REFERENCES "zones"("id") ON DELETE CASCADE ON UPDATE CASCADE;
