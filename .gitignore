# Dependencies
node_modules/
pnpm-lock.yaml

# Build outputs
dist/
build/
.next/
out/

# Environment files
.env
.env.*
!.env.example

# IDE/Editor
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Docker
.docker/
docker-compose.override.yml
*.volume/

# Testing
coverage/
.nyc_output/

# OS specific
.DS_Store
Thumbs.db
desktop.ini

# Temporary files
*.swp
*.swo
*~
.temp/
.tmp/
.cache/

# Project specific
.version

#prisma
/generated/prisma

.pnpm-store