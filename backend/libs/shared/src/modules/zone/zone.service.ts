import {
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { Zone } from '../../repositories/models/zone.model';
import { ZoneRepository } from '../../repositories/zone.repository';
import { H3IndexToZoneRepository } from '../../repositories/h3IndexToZone.repository';
import { PrismaService } from '../../database/prisma/prisma.service';

@Injectable()
export class ZoneService {
  constructor(
    private readonly zoneRepository: ZoneRepository,
    private readonly h3IndexToZoneRepository: H3IndexToZoneRepository,
    private readonly prisma: PrismaService,
  ) { }

  async createZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    return this.prisma.$transaction(async () => {
      // Create the zone
      const zone = await this.zoneRepository.createZone(data);

      // Create H3 index mappings
      if (data.h3Indexes && data.h3Indexes.length > 0) {
        await this.h3IndexToZoneRepository.createManyH3IndexToZones(
          data.h3Indexes.map(h3Index => ({
            h3Index,
            zoneId: zone.id,
          })),
        );
      }

      return zone;
    });
  }

  async createCityZone(
    name: string,
    polygon: any,
    h3Indexes: bigint[],
  ): Promise<Zone> {
    const zoneData = {
      name,
      isCity: true,
      polygon,
      h3Indexes,
      cityId: null, // City zones have cityId as null
    };

    return this.createZone(zoneData);
  }

  async findAllZones(): Promise<Zone[]> {
    return this.zoneRepository.findAllZones();
  }

  async findZoneById(id: string): Promise<Zone> {
    const zone = await this.zoneRepository.findZoneById(id);
    if (!zone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }
    return zone;
  }

  async findZonesByCity(cityId: string): Promise<Zone[]> {
    return this.zoneRepository.findZonesByCity(cityId);
  }

  async findCityZone(cityId: string): Promise<Zone | null> {
    return this.zoneRepository.findCityZone(cityId);
  }

  async findZonesByH3Index(h3Index: bigint): Promise<Zone[]> {
    return this.zoneRepository.findZonesByH3Index(h3Index);
  }

  async updateZone(id: string, data: Partial<Zone>): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Update the zone
      const updatedZone = await this.zoneRepository.updateZone(id, data);

      // Update H3 index mappings if h3Indexes are provided
      if (data.h3Indexes !== undefined) {
        await this.h3IndexToZoneRepository.bulkUpsertH3IndexToZones(
          id,
          data.h3Indexes,
        );
      }

      return updatedZone;
    });
  }

  async deleteZone(id: string): Promise<Zone> {
    const existingZone = await this.zoneRepository.findZoneById(id);
    if (!existingZone) {
      throw new NotFoundException(`Zone with ID ${id} not found`);
    }

    return this.prisma.$transaction(async () => {
      // Delete H3 index mappings first
      await this.h3IndexToZoneRepository.deleteH3IndexToZonesByZone(id);

      // Then delete the zone
      return this.zoneRepository.deleteZone(id);
    });
  }

  async paginateZones(
    page = 1,
    limit = 10,
    filters?: {
      isCity?: boolean;
      cityId?: string;
      search?: string;
    },
  ) {
    const options: any = {};

    if (filters) {
      const whereConditions: any = {};

      if (filters.isCity !== undefined) {
        whereConditions.isCity = filters.isCity;
      }

      if (filters.cityId) {
        whereConditions.cityId = filters.cityId;
      }

      if (filters.search) {
        whereConditions.name = {
          contains: filters.search,
          mode: 'insensitive',
        };
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }
    }

    return this.zoneRepository.paginateZones(page, limit, options);
  }
}
