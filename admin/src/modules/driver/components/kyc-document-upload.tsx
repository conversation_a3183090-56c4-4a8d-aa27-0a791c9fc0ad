'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
   Dialog,
   DialogContent,
   DialogDescription,
   DialogHeader,
   DialogTitle,
} from '@/components/ui/dialog';
import { useFileUpload, formatBytes } from '@/hooks/use-file-upload';
import { toast } from '@/lib/toast';
import { AlertCircle, FileText, Upload, X, Loader2 } from 'lucide-react';
import { useState, useEffect } from 'react';
import { useFileUpload as useFileUploadMutation, useAddKycDocument, useEditKycDocument } from '../api/mutations';
import { KycDocument } from '../types/driver';

interface KycDocumentUploadProps {
   document: KycDocument;
   driverId: string;
   open: boolean;
   onClose: () => void;
   onSuccess: () => void;
   editMode?: boolean; // Whether we're editing an existing document (re-upload)
}

export function KycDocumentUpload({ document, driverId, open, onClose, onSuccess, editMode = false }: KycDocumentUploadProps) {
   const [isSubmitting, setIsSubmitting] = useState(false);
   
   const fileUploadMutation = useFileUploadMutation();
   const addKycDocumentMutation = useAddKycDocument();
   const editKycDocumentMutation = useEditKycDocument();

   const maxSize = 10 * 1024 * 1024; // 10MB
   const acceptedTypes = '.pdf,.jpg,.jpeg,.png';

   const [
      { files, isDragging, errors },
      {
         handleDragEnter,
         handleDragLeave,
         handleDragOver,
         handleDrop,
         openFileDialog,
         removeFile,
         getInputProps,
         clearFiles,
         clearErrors,
      },
   ] = useFileUpload({
      maxSize,
      accept: acceptedTypes,
      multiple: false,
   });

   const file = files[0];

   // Check if document supports Digilocker (only Aadhaar card and driving license)
   const supportsDigilocker = document.identifier === 'aadhaar_card' || document.identifier === 'driving_licence';

   // Clear files and errors when modal opens
   useEffect(() => {
      if (open) {
         clearFiles();
         clearErrors();
      }
   }, [open, clearFiles, clearErrors]);

   // Clear files and errors when modal is closed
   const handleClose = () => {
      clearFiles();
      clearErrors();
      onClose();
   };

   const handleSubmit = async () => {
      if (!file) {
         toast.error('Please select a file to upload');
         return;
      }

      setIsSubmitting(true);

      try {
         // Step 1: Upload file to S3
         const uploadResponse = await fileUploadMutation.mutateAsync(file.file as File);
         
         if (editMode && document.driverKyc) {
            // Step 2a: Edit existing KYC document (re-upload scenario)
            await editKycDocumentMutation.mutateAsync({
               id: document.driverKyc.id,
               documentUrl: uploadResponse.data.key, // Use the key, not the URL
            });
            toast.success('Document re-uploaded successfully');
         } else {
            // Step 2b: Add new KYC document
            await addKycDocumentMutation.mutateAsync({
               userProfileId: driverId,
               kycDocumentId: document.id,
               documentUrl: uploadResponse.data.key, // Use the key, not the URL
               fromDigilocker: false,
            });
            toast.success('Document uploaded successfully');
         }

         onSuccess();
      } catch (error) {
         console.error('Upload error:', error);
         toast.error('Failed to upload document. Please try again.');
      } finally {
         setIsSubmitting(false);
      }
   };


   return (
      <Dialog open={open} onOpenChange={handleClose}>
         <DialogContent 
            onInteractOutside={e => {
               e.preventDefault();
            }}
            className='max-w-2xl max-h-[90vh] overflow-y-auto'
         >
            <DialogHeader>
               <DialogTitle>Upload {document.name}</DialogTitle>
               <DialogDescription>
                  Upload your {document.name.toLowerCase()} document for verification
               </DialogDescription>
            </DialogHeader>

            <div className='space-y-6'>
               {/* Digilocker Option - Only for Aadhaar card and driving license */}
               {supportsDigilocker && (
                  <>
                     <Card className='p-4 bg-blue-50 border-blue-200'>
                        <div className='flex items-center justify-between'>
                           <div>
                              <h4 className='font-medium text-blue-900'>Fetch from Digilocker</h4>
                              <p className='text-sm text-blue-700 mt-1'>
                                 Get your document directly from Digilocker
                              </p>
                           </div>
                           <Button variant='outline' disabled className='bg-white'>
                              Fetch from Digilocker
                           </Button>
                        </div>
                     </Card>

                     <div className='text-center text-sm text-gray-500'>OR UPLOAD MANUALLY</div>
                  </>
               )}

               {/* File Upload Area */}
               <div className='space-y-4'>
                  <div
                     role='button'
                     onClick={openFileDialog}
                     onDragEnter={handleDragEnter}
                     onDragLeave={handleDragLeave}
                     onDragOver={handleDragOver}
                     onDrop={handleDrop}
                     data-dragging={isDragging || undefined}
                     className='border-input hover:bg-accent/50 data-[dragging=true]:bg-accent/50 has-[input:focus]:border-ring has-[input:focus]:ring-ring/50 flex min-h-40 flex-col items-center justify-center rounded-xl border border-dashed p-4 transition-colors has-disabled:pointer-events-none has-disabled:opacity-50 has-[input:focus]:ring-[3px]'
                  >
                     <input
                        {...getInputProps()}
                        className='sr-only'
                        aria-label='Upload file'
                        disabled={Boolean(file)}
                     />

                     <div className='flex flex-col items-center justify-center text-center'>
                        <div
                           className='bg-background mb-2 flex size-11 shrink-0 items-center justify-center rounded-full border'
                           aria-hidden='true'
                        >
                           <Upload className='size-4 opacity-60' />
                        </div>
                        <p className='mb-1.5 text-sm font-medium'>Upload Document</p>
                        <p className='text-muted-foreground text-xs'>
                           Drag & drop or click to browse (max. {formatBytes(maxSize)})
                        </p>
                        <p className='text-muted-foreground text-xs mt-1'>
                           Supported: JPEG, PNG, PDF
                        </p>
                     </div>
                  </div>

                  {errors.length > 0 && (
                     <div className='text-destructive flex items-center gap-1 text-xs' role='alert'>
                        <AlertCircle className='size-3 shrink-0' />
                        <span>{errors[0]}</span>
                     </div>
                  )}

                  {/* File Preview */}
                  {file && (
                     <Card className='p-4'>
                        <div className='flex items-center justify-between'>
                           <div className='flex items-center gap-3'>
                              <div className='flex items-center justify-center w-10 h-10 bg-blue-100 rounded-lg'>
                                 <FileText className='w-5 h-5 text-blue-600' />
                              </div>
                              <div>
                                 <p className='text-sm font-medium text-gray-900'>{file.file.name}</p>
                                 <p className='text-xs text-gray-500'>{formatBytes(file.file.size)}</p>
                              </div>
                           </div>
                           <Button
                              variant='ghost'
                              size='sm'
                              onClick={() => removeFile(file.id)}
                              className='text-red-600 hover:text-red-700'
                           >
                              <X className='w-4 h-4' />
                           </Button>
                        </div>
                     </Card>
                  )}


                  {/* Action Buttons */}
                  <div className='flex gap-3 pt-4'>
                     <Button variant='outline' onClick={handleClose} className='flex-1'>
                        Cancel
                     </Button>
                     <Button
                        onClick={handleSubmit}
                        disabled={!file || isSubmitting}
                        className='flex-1 gap-2'
                     >
                        {isSubmitting ? (
                           <>
                              <Loader2 className='w-4 h-4 animate-spin' />
                              Uploading...
                           </>
                        ) : (
                           <>
                              <Upload className='w-4 h-4' />
                              Submit
                           </>
                        )}
                     </Button>
                  </div>
               </div>
            </div>
         </DialogContent>
      </Dialog>
   );
}
