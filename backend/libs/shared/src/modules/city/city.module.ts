import { Module } from '@nestjs/common';
import { CityRepository } from '../../repositories/city.repository';
import { PrismaService } from '../../database/prisma/prisma.service';
import { CityService } from './city.service';
import { FileUploadService } from '../../common/file-upload/aws-s3/aws-file-upload.servie';
import { AppConfigModule } from '../../config';

@Module({
  imports: [AppConfigModule],
  providers: [CityRepository, CityService, PrismaService, FileUploadService],
  exports: [CityRepository, CityService],
})
export class CityModule {}
