'use client';

import { Badge } from '@/components/ui/badge';
import { Driver } from '../types/driver';

interface DriverPersonalDetailsProps {
   driver: Driver;
}

export function DriverPersonalDetails({ driver }: DriverPersonalDetailsProps) {
   const formatDate = (dateString: string | null) => {
      if (!dateString) return 'Not provided';
      return new Date(dateString).toLocaleDateString('en-US', {
         year: 'numeric',
         month: 'long',
         day: 'numeric',
      });
   };

   return (
      <div className='space-y-6'>
         {/* Main 2-column grid for basic and contact info */}
         <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
            {/* Left Column - Basic Information */}
            <div className='space-y-4'>
               <h3 className='text-lg font-semibold text-gray-900'>Basic Information</h3>
               <div className='space-y-3'>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>Full Name</span>
                     <span className='text-sm text-gray-900'>
                        {`${driver.firstName || ''} ${driver.lastName || ''}`.trim() || 'Not provided'}
                     </span>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>Gender</span>
                     <span className='text-sm text-gray-900'>
                        {driver.gender
                           ? driver.gender.charAt(0) + driver.gender.slice(1).toLowerCase()
                           : 'Not provided'}
                     </span>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>Date of Birth</span>
                     <span className='text-sm text-gray-900'>{formatDate(driver.dob)}</span>
                  </div>
               </div>
            </div>

            {/* Right Column - Contact Information */}
            <div className='space-y-4'>
               <h3 className='text-lg font-semibold text-gray-900'>Contact Information</h3>
               <div className='space-y-3'>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>Phone Number</span>
                     <div className='flex items-center gap-2'>
                        <span className='text-sm text-gray-900'>{driver.phoneNumber}</span>
                        <Badge
                           className={`text-xs ${
                              driver.phoneVerified
                                 ? 'bg-green-100 text-green-700'
                                 : 'bg-gray-100 text-gray-700'
                           }`}
                        >
                           {driver.phoneVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                     </div>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>Email Address</span>
                     <div className='flex items-center gap-2'>
                        <span className='text-sm text-gray-900'>{driver.email || 'Not provided'}</span>
                        {driver.email && (
                           <Badge
                              className={`text-xs ${
                                 driver.emailVerified
                                    ? 'bg-green-100 text-green-700'
                                    : 'bg-gray-100 text-gray-700'
                              }`}
                           >
                              {driver.emailVerified ? 'Verified' : 'Unverified'}
                           </Badge>
                        )}
                     </div>
                  </div>
                  <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                     <span className='text-sm font-medium text-gray-600'>City</span>
                     <span className='text-sm text-gray-900'>
                        {driver.cityName || 'Not specified'}
                     </span>
                  </div>
               </div>
            </div>
         </div>

         {/* System Information - Full width at bottom */}
         <div className='space-y-4'>
            <h3 className='text-lg font-semibold text-gray-900'>System Information</h3>
            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
               <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                  <span className='text-sm font-medium text-gray-600'>User ID</span>
                  <span className='text-sm text-gray-900 font-mono'>{driver.userId}</span>
               </div>
               <div className='bg-gray-50 border border-gray-200 rounded-lg p-4 h-12 flex items-center justify-between'>
                  <span className='text-sm font-medium text-gray-600'>Driver ID</span>
                  <span className='text-sm text-gray-900 font-mono'>{driver.id}</span>
               </div>
            </div>
         </div>
      </div>
   );
}
