import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { H3IndexToZone } from './models/h3IndexToZone.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class H3IndexToZoneRepository extends BaseRepository<H3IndexToZone> {
  protected readonly modelName = 'h3IndexToZone';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createH3IndexToZone(
    data: Omit<H3IndexToZone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<H3IndexToZone> {
    return this.create(data, {
      include: {
        zone: true,
      },
    });
  }

  async createManyH3IndexToZones(
    data: Omit<H3IndexToZone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>[],
  ): Promise<number> {
    const result = await this.prisma.h3IndexToZone.createMany({
      data: data.map(item => ({
        h3Index: item.h3Index,
        zoneId: item.zoneId,
      })),
      skipDuplicates: true,
    });
    return result.count;
  }

  async findH3IndexToZonesByZone(zoneId: string): Promise<H3IndexToZone[]> {
    return this.findMany({
      where: {
        zoneId,
      },
      include: {
        zone: true,
      },
    });
  }

  async findZonesByH3Index(h3Index: bigint): Promise<H3IndexToZone[]> {
    return this.findMany({
      where: {
        h3Index,
      },
      include: {
        zone: true,
      },
    });
  }

  async findH3IndexToZoneByH3IndexAndZone(
    h3Index: bigint,
    zoneId: string,
  ): Promise<H3IndexToZone | null> {
    return this.findOne({
      where: {
        h3Index,
        zoneId,
      },
      include: {
        zone: true,
      },
    });
  }

  async deleteH3IndexToZonesByZone(zoneId: string): Promise<number> {
    const result = await this.prisma.h3IndexToZone.deleteMany({
      where: {
        zoneId,
      },
    });
    return result.count;
  }

  async deleteH3IndexToZone(id: string): Promise<H3IndexToZone> {
    return this.softDeleteById(id);
  }

  async bulkUpsertH3IndexToZones(
    zoneId: string,
    h3Indexes: bigint[],
  ): Promise<void> {
    // First, delete existing mappings for this zone
    await this.deleteH3IndexToZonesByZone(zoneId);

    // Then, create new mappings
    if (h3Indexes.length > 0) {
      await this.createManyH3IndexToZones(
        h3Indexes.map(h3Index => ({
          h3Index,
          zoneId,
        })),
      );
    }
  }
}
