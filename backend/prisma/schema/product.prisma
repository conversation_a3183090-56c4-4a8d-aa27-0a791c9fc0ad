model Product {
  id               String    @id @default(uuid()) @db.Uuid
  vehicleTypeId    String    @map("vehicle_type_id") @db.Uuid
  productServiceId String?    @map("product_service_id") @db.Uuid
  name             String
  description      String?
  identifier       String?
  icon             String?
  isEnabled        <PERSON><PERSON>an   @default(true) @map("is_enabled")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at") @db.Timestamptz

  // Relations
  vehicleType    VehicleType    @relation(fields: [vehicleTypeId], references: [id])
  productService ProductService? @relation(fields: [productServiceId], references: [id])

  cityProducts CityProduct[]

  @@map("products")
}
