import { <PERSON>, Logger } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { IEventName } from '@shared/shared/event-emitter';

@Controller()
export class AuditLogConsumer {
  private readonly logger = new Logger(AuditLogConsumer.name);

  @EventPattern(IEventName.DRIVER_CREATED)
  handleDriverCreated(@Payload() message: any) {
    this.logger.log(`[AUDIT] Received DRIVER_CREATED event`);
    this.logger.log(JSON.stringify(message.value));
    // Store audit log to DB here
  }

}
