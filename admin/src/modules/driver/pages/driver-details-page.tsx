'use client';

import { Card } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, ArrowLeft, Mail, MapPin, Phone, User } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useState } from 'react';
import { useGetDriver } from '../api/queries';
import { DriverPersonalDetails } from '../components/driver-personal-details';
import { DriverKycDocuments } from '../components/driver-kyc-documents';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useParams } from 'next/navigation';

export function DriverDetailsPage() {
   const params = useParams<{ id: string }>();
   const driverId = params.id;
   const [activeTab, setActiveTab] = useState('personal');
   const { data: driver, isLoading, error } = useGetDriver(driverId);

   if (isLoading) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='animate-pulse'>
               <div className='h-8 bg-gray-200 rounded w-1/4 mb-4'></div>
               <div className='h-32 bg-gray-200 rounded mb-4'></div>
               <div className='h-96 bg-gray-200 rounded'></div>
            </div>
         </div>
      );
   }

   if (error || !driver?.data) {
      return (
         <div className='flex flex-1 flex-col gap-4 p-4'>
            <div className='flex items-center gap-4 mb-6'>
               <Link href='/dashboard/drivers'>
                  <Button variant='ghost' size='sm' className='gap-2'>
                     <ArrowLeft className='h-4 w-4' />
                     Back to Drivers
                  </Button>
               </Link>
            </div>
            <Card className='p-8 text-center'>
               <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
               <h3 className='text-lg font-medium text-gray-900 mb-2'>Driver Not Found</h3>
               <p className='text-gray-600'>
                  The driver you're looking for doesn't exist or has been removed.
               </p>
            </Card>
         </div>
      );
   }

   const driverData = driver.data;
   const fullName = `${driverData.firstName || ''} ${driverData.lastName || ''}`.trim() || 'N/A';

   return (
      <div className='flex flex-1 flex-col space-y-6 p-6'>
         {/* Clean Header */}
         <div className='flex items-center gap-3'>
            <Link href='/dashboard/drivers'>
               <Button variant='ghost' size='sm' className='gap-2 text-gray-600 hover:text-gray-900'>
                  <ArrowLeft className='h-4 w-4' />
                  Back to Drivers
               </Button>
            </Link>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>Drivers</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-600'>Driver Details</span>
            <span className='text-gray-300'>/</span>
            <span className='text-sm text-gray-900 font-medium'>{fullName}</span>
         </div>

         {/* Compact Driver Header */}
         <Card className='p-6'>
            <div className='flex items-center gap-4'>
               {/* Smaller Profile Picture */}
               <div className='flex-shrink-0'>
                  {driverData.profilePictureUrl ? (
                     <Image
                        src={driverData.profilePictureUrl}
                        alt={fullName}
                        width={56}
                        height={56}
                        className='w-14 h-14 rounded-full object-cover'
                     />
                  ) : (
                     <div className='w-14 h-14 rounded-full bg-gray-100 flex items-center justify-center'>
                        <User className='w-6 h-6 text-gray-400' />
                     </div>
                  )}
               </div>

               {/* Clean Driver Info */}
               <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-3 mb-2'>
                     <h1 className='text-xl font-semibold text-gray-900 truncate'>{fullName}</h1>
                     <Badge 
                        variant={driverData.phoneVerified ? 'default' : 'secondary'}
                        className={`text-xs ${
                           driverData.phoneVerified 
                              ? 'bg-green-100 text-green-700' 
                              : 'bg-gray-100 text-gray-700'
                        }`}
                     >
                        {driverData.phoneVerified ? 'Verified' : 'Unverified'}
                     </Badge>
                  </div>

                  <div className='flex items-center gap-6 text-sm text-gray-600'>
                     <div className='flex items-center gap-2'>
                        <Mail className='w-4 h-4 text-gray-400' />
                        <span className='truncate'>{driverData.email || 'No email'}</span>
                     </div>
                     <div className='flex items-center gap-2'>
                        <Phone className='w-4 h-4 text-gray-400' />
                        <span>{driverData.phoneNumber}</span>
                     </div>
                     <div className='flex items-center gap-2'>
                        <MapPin className='w-4 h-4 text-gray-400' />
                        <span>{driverData.cityName || 'Not specified'}</span>
                     </div>
                  </div>
               </div>
            </div>
         </Card>

         {/* Clean Tabs */}
         <Card>
            <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
               <div className='border-b border-gray-200 px-6 pt-4 pb-0'>
                  <TabsList className='grid w-full grid-cols-2 max-w-sm bg-gray-50'>
                     <TabsTrigger value='personal' className='text-sm'>Personal Details</TabsTrigger>
                     <TabsTrigger value='kyc' className='text-sm'>KYC</TabsTrigger>
                  </TabsList>
               </div>

               <div className='p-6'>
                  <TabsContent value='personal' className='mt-0'>
                     <DriverPersonalDetails driver={driverData} />
                  </TabsContent>

                  <TabsContent value='kyc' className='mt-0'>
                     <DriverKycDocuments driverId={driverId} />
                  </TabsContent>
               </div>
            </Tabs>
         </Card>
      </div>
   );
}
