'use client';

import { Card } from '@/components/ui/card';
import { useState } from 'react';
import { useCities, useListDriver } from '../api/queries';
import { CreateDriver } from '../components/create-driver';
import { DriverFilters } from '../components/driver-filters';
import { DriverTable } from '../components/driver-table';

export function DriverPage() {
   const [page, setPage] = useState(1);
   const [limit] = useState(10);
   const [search, setSearch] = useState('');
   const [cityId, setCityId] = useState<string | undefined>(undefined);
   const [name, setName] = useState<string | undefined>(undefined);
   const [email, setEmail] = useState<string | undefined>(undefined);
   const [phoneNumber, setPhoneNumber] = useState<string | undefined>(undefined);

   // preload cities
   useCities();

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleCityChange = (value: string | undefined) => {
      setCityId(value);
      setPage(1);
   };

   const handleNameChange = (value: string | undefined) => {
      setName(value);
      setPage(1);
   };

   const handleEmailChange = (value: string | undefined) => {
      setEmail(value);
      setPage(1);
   };

   const handlePhoneChange = (value: string | undefined) => {
      setPhoneNumber(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setCityId(undefined);
      setName(undefined);
      setEmail(undefined);
      setPhoneNumber(undefined);
      setPage(1);
   };

   const listDriver = useListDriver({
      page,
      limit,
      search: search || undefined,
      cityId: cityId || undefined,
      name: name || undefined,
      email: email || undefined,
      phoneNumber: phoneNumber || undefined,
   });

   // Calculate total driver count from API meta data
   const totalDrivers = listDriver.data?.meta?.total || 0;

   return (
      <div className='flex flex-1 flex-col gap-4 p-4'>
         <div className='flex justify-between items-center'>
            <h2 className='text-2xl font-semibold text-gray-900'>Drivers</h2>
            <div className='flex items-center gap-4'>
               {/* Driver Info Cards */}
               <div className='flex gap-2'>
                  <div className='flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border'>
                     <span className='text-sm text-gray-600'>Total Drivers</span>
                     <span className='inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-blue-700 bg-blue-100 rounded-full'>
                        {totalDrivers}
                     </span>
                  </div>
               </div>
               <CreateDriver />
            </div>
         </div>

         <Card className='overflow-hidden py-4 px-4 rounded-sm'>
            <DriverFilters
               search={search}
               cityId={cityId}
               name={name}
               email={email}
               phoneNumber={phoneNumber}
               onSearchChange={handleSearchChange}
               onCityChange={handleCityChange}
               onNameChange={handleNameChange}
               onEmailChange={handleEmailChange}
               onPhoneChange={handlePhoneChange}
               isLoading={listDriver.isFetching && !listDriver.isLoading}
            />

            <DriverTable
               data={listDriver.data}
               isLoading={listDriver.isLoading}
               currentPage={page}
               onPageChange={(newPage: number) => setPage(newPage)}
               hasFilters={!!search || !!cityId || !!name || !!email || !!phoneNumber}
               hasSearch={!!search}
               hasStatus={false}
               hasLocation={!!cityId}
               onClearFilters={handleClearFilters}
            />
         </Card>
      </div>
   );
}
