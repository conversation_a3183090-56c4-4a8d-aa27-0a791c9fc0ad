import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { Zone } from './models/zone.model';
import { PrismaService } from '../database/prisma/prisma.service';

@Injectable()
export class ZoneRepository extends BaseRepository<Zone> {
  protected readonly modelName = 'zone';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createZone(
    data: Omit<Zone, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<Zone> {
    return this.create(data, {
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async findAllZones(): Promise<Zone[]> {
    return this.findMany({
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async findZoneById(id: string): Promise<Zone | null> {
    return this.findById(id, {
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async findZonesByCity(cityId: string): Promise<Zone[]> {
    return this.findMany({
      where: {
        cityId,
      },
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async findCityZone(_cityId: string): Promise<Zone | null> {
    return this.findOne({
      where: {
        isCity: true,
        cityId: null, // City zones have cityId as null
      },
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async findZonesByH3Index(h3Index: bigint): Promise<Zone[]> {
    return this.findMany({
      where: {
        h3Indexes: {
          has: h3Index,
        },
      },
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async updateZone(id: string, data: Partial<Zone>): Promise<Zone> {
    return this.updateById(id, data, {
      include: {
        h3IndexToZones: true,
      },
    });
  }

  async deleteZone(id: string): Promise<Zone> {
    return this.softDeleteById(id);
  }

  async paginateZones(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      include: {
        h3IndexToZones: true,
      },
      ...options,
    });
  }
}
