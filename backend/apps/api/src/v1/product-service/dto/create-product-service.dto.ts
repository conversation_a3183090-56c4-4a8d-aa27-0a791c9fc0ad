import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

export class CreateProductServiceDto {
  @ApiProperty({
    example: 'Ride Sharing',
    description: 'Name of the product service',
  })
  @IsString()
  @IsNotEmpty()
  name!: string;

  @ApiProperty({
    example: 'On-demand ride sharing service',
    description: 'Description of the product service',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    example: 'uploads/icons/ride-sharing.png',
    description: 'Icon file path for the product service',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    example: 'ride_sharing',
    description:
      'Unique identifier for the product service (auto-generated if not provided)',
    required: false,
  })
  @IsOptional()
  @IsString()
  identifier?: string;
}
