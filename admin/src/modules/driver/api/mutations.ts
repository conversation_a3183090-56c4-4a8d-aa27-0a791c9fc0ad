import { apiClient } from '@/lib/api-client';
import { useMutation } from '@tanstack/react-query';
import {
   CreateDriverRequest,
   DriverOtpVerificationRequest,
   DriverRegistrationRequest,
   DriverRegistrationResponse,
   DriverResendOtpRequest,
   DriverResponse,
   UpdateDriverRequest,
   FileUploadResponse,
   AddKycDocumentRequest,
   AddKycDocumentResponse,
} from '../types/driver';

/**
 * Hook for creating a new driver profile (after OTP verification)
 */
export const useCreateDriver = () => {
   return useMutation({
      mutationFn: async (data: CreateDriverRequest): Promise<DriverResponse> => {
         return apiClient.post('/drivers/admin', data);
      },
   });
};

/**
 * Hook for updating a driver profile
 */
export const useUpdateDriver = () => {
   return useMutation({
      mutationFn: async (data: { id: string } & UpdateDriverRequest): Promise<DriverResponse> => {
         const { id, ...payload } = data;
         return apiClient.patch(`/drivers/admin/${id}`, payload);
      },
   });
};

/**
 * Hook for deleting a driver
 */
export const useDeleteDriver = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<void> => {
         return apiClient.delete(`/drivers/admin/${id}`);
      },
   });
};

/**
 * Hook for registering a new driver with phone number
 */
export const useRegisterDriver = () => {
   return useMutation({
      mutationFn: async (data: DriverRegistrationRequest): Promise<DriverRegistrationResponse> => {
         return apiClient.post('/drivers/admin/register', data);
      },
   });
};

/**
 * Hook for verifying driver OTP
 */
export const useVerifyDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverOtpVerificationRequest
      ): Promise<{ success: boolean; message: string; data?: any; timestamp: number }> => {
         return apiClient.post('/drivers/admin/verify-otp', data);
      },
   });
};

/**
 * Hook for resending driver OTP
 */
export const useResendDriverOtp = () => {
   return useMutation({
      mutationFn: async (
         data: DriverResendOtpRequest
      ): Promise<{ success: boolean; message: string; timestamp: number }> => {
         return apiClient.post('/drivers/admin/resend-otp', data);
      },
   });
};

/**
 * Hook for uploading files to S3
 */
export const useFileUpload = () => {
   return useMutation({
      mutationFn: async (file: File): Promise<FileUploadResponse> => {
         const formData = new FormData();
         formData.append('file', file);
         return apiClient.post('/file-upload/upload', formData, {
            headers: {
               'Content-Type': 'multipart/form-data',
            },
         });
      },
   });
};

/**
 * Hook for deleting files from S3
 */
export const useFileDelete = () => {
   return useMutation({
      mutationFn: async (key: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.delete('/file-upload/delete', {
            data: { key },
         });
      },
   });
};

/**
 * Hook for adding KYC document
 */
export const useAddKycDocument = () => {
   return useMutation({
      mutationFn: async (data: AddKycDocumentRequest): Promise<AddKycDocumentResponse> => {
         return apiClient.post('/driver-kyc/admin/add-kyc-document', data);
      },
   });
};

/**
 * Hook for deleting KYC document
 */
export const useDeleteKycDocument = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.delete(`/driver-kyc/${id}`);
      },
   });
};

/**
 * Hook for approving KYC document
 */
export const useApproveKycDocument = () => {
   return useMutation({
      mutationFn: async (id: string): Promise<{ success: boolean; message: string }> => {
         return apiClient.patch(`/driver-kyc/${id}/accept`);
      },
   });
};

/**
 * Hook for rejecting KYC document
 */
export const useRejectKycDocument = () => {
   return useMutation({
      mutationFn: async (data: { id: string; rejectionNote: string }): Promise<{ success: boolean; message: string }> => {
         const { id, rejectionNote } = data;
         return apiClient.patch(`/driver-kyc/${id}/reject`, { rejectionNote });
      },
   });
};

/**
 * Hook for editing KYC document (re-upload)
 */
export const useEditKycDocument = () => {
   return useMutation({
      mutationFn: async (data: { id: string; documentUrl: string }): Promise<{ success: boolean; message: string }> => {
         const { id, documentUrl } = data;
         return apiClient.patch(`/driver-kyc/admin/edit-kyc-document/${id}`, { 
            documentUrl,
         });
      },
   });
};
