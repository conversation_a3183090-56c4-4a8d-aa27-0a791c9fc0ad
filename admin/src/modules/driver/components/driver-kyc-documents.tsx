'use client';

import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
   AlertDialog,
   AlertDialogAction,
   AlertDialogCancel,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { AlertCircle, FileText, Trash2, Upload, Check, X } from 'lucide-react';
import { useGetDriverKycDocuments } from '../api/queries';
import { useDeleteKycDocument } from '../api/mutations';
import { KycDocument } from '../types/driver';
import { KycDocumentUpload } from './kyc-document-upload';
import { ApproveKycModal } from './approve-kyc-modal';
import { RejectKycModal } from './reject-kyc-modal';
import { useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from '@/lib/toast';

interface DriverKycDocumentsProps {
   driverId: string;
}

export function DriverKycDocuments({ driverId }: DriverKycDocumentsProps) {
   const [uploadingDocumentId, setUploadingDocumentId] = useState<string | null>(null);
   const [approvingDocument, setApprovingDocument] = useState<{ id: string; name: string } | null>(null);
   const [rejectingDocument, setRejectingDocument] = useState<{ id: string; name: string } | null>(null);
   const queryClient = useQueryClient();

   const { data: kycDocuments, isLoading, error } = useGetDriverKycDocuments(driverId);
   const deleteKycMutation = useDeleteKycDocument();

   const handleDeleteDocument = async (documentId: string) => {
      try {
         await deleteKycMutation.mutateAsync(documentId);
         toast.success('Document deleted successfully');
         queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] });
      } catch {
         toast.error('Failed to delete document');
      }
   };


   const getStatusBadge = (status: string) => {
      switch (status) {
         case 'APPROVED':
            return (
               <Badge className='bg-green-100 text-green-700 text-xs font-medium px-2 py-1'>
                  Approved
               </Badge>
            );
         case 'REJECTED':
            return (
               <Badge className='bg-red-100 text-red-700 text-xs font-medium px-2 py-1'>
                  Rejected
               </Badge>
            );
         case 'PENDING':
         default:
            return (
               <Badge className='bg-yellow-100 text-yellow-700 text-xs font-medium px-2 py-1'>
                  Pending
               </Badge>
            );
      }
   };

   if (isLoading) {
      return (
         <div className='space-y-6'>
            {[1, 2, 3].map(i => (
               <Card key={i} className='p-6'>
                  <div className='animate-pulse'>
                     <div className='h-6 bg-gray-200 rounded w-1/4 mb-4'></div>
                     <div className='h-32 bg-gray-200 rounded'></div>
                  </div>
               </Card>
            ))}
         </div>
      );
   }

   if (error || !kycDocuments?.data) {
      return (
         <Card className='p-8 text-center'>
            <AlertCircle className='w-12 h-12 text-red-500 mx-auto mb-4' />
            <h3 className='text-lg font-medium text-gray-900 mb-2'>Failed to Load KYC Documents</h3>
            <p className='text-gray-600'>
               There was an error loading the KYC documents. Please try again.
            </p>
         </Card>
      );
   }

   return (
      <div className='space-y-6'>
         <div>
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>KYC Verification</h3>
            <p className='text-sm text-gray-600'>Verify your identity documents.</p>
         </div>

         {kycDocuments.data.map((document: KycDocument) => (
            <div key={document.id} className='border border-gray-200 rounded-lg p-6 bg-white'>
               <div className='flex items-center justify-between mb-4'>
                  <div className='flex items-center gap-3'>
                     <div className='w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center'>
                        <FileText className='w-4 h-4 text-gray-500' />
                     </div>
                     <div>
                        <h4 className='font-medium text-gray-900'>{document.name}</h4>
                        {document.isMandatory && (
                           <span className='text-xs text-gray-500'>Required</span>
                        )}
                     </div>
                  </div>

                  {document.driverKyc && (
                     <div className='flex items-center gap-2'>
                        {getStatusBadge(document.driverKyc.status)}
                     </div>
                  )}
               </div>

               {document.driverKyc ? (
                  <div className='space-y-4'>
                     {/* Document Info */}
                     <div className='bg-gray-50 rounded-lg p-4'>
                        <div className='flex items-center justify-between mb-3'>
                           <span className='text-sm font-medium text-gray-900'>Document Uploaded</span>
                           <div className='flex items-center gap-2'>
                              <Button
                                 variant='outline'
                                 size='sm'
                                 onClick={() =>
                                    window.open(document.driverKyc!.documentUrl, '_blank')
                                 }
                                 className='text-blue-600 hover:text-blue-700 border-blue-200 hover:border-blue-300'
                              >
                                 View Document
                              </Button>
                              <AlertDialog>
                                 <AlertDialogTrigger asChild>
                                    <Button
                                       variant='outline'
                                       size='sm'
                                       disabled={deleteKycMutation.isPending}
                                       className='text-red-600 hover:text-red-700 border-red-200 hover:border-red-300'
                                    >
                                       <Trash2 className='w-4 h-4' />
                                    </Button>
                                 </AlertDialogTrigger>
                                 <AlertDialogContent>
                                    <AlertDialogHeader>
                                       <AlertDialogTitle>Delete Document</AlertDialogTitle>
                                       <AlertDialogDescription>
                                          Are you sure you want to delete this document? This action
                                          cannot be undone.
                                       </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                       <AlertDialogCancel>Cancel</AlertDialogCancel>
                                       <AlertDialogAction
                                          onClick={() =>
                                             handleDeleteDocument(document.driverKyc!.id)
                                          }
                                          className='bg-red-600 hover:bg-red-700'
                                       >
                                          Delete
                                       </AlertDialogAction>
                                    </AlertDialogFooter>
                                 </AlertDialogContent>
                              </AlertDialog>
                           </div>
                        </div>

                        <div className='grid grid-cols-1 md:grid-cols-2 gap-4 text-sm'>
                           {document.driverKyc.documentNumber && (
                              <div>
                                 <span className='text-gray-500'>Document Number: </span>
                                 <span className='text-gray-900'>{document.driverKyc.documentNumber}</span>
                              </div>
                           )}
                           <div>
                              <span className='text-gray-500'>Uploaded: </span>
                              <span className='text-gray-900'>
                                 {new Date(document.driverKyc.createdAt).toLocaleDateString()}
                              </span>
                           </div>
                        </div>
                     </div>

                     {/* Rejection Note */}
                     {document.driverKyc.status === 'REJECTED' &&
                        document.driverKyc.rejectionNote && (
                           <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
                              <div className='flex items-start gap-2'>
                                 <AlertCircle className='w-4 h-4 text-red-600 mt-0.5' />
                                 <div>
                                    <h5 className='text-sm font-medium text-red-800 mb-1'>
                                       Rejection Reason
                                    </h5>
                                    <p className='text-sm text-red-700'>
                                       {document.driverKyc.rejectionNote}
                                    </p>
                                 </div>
                              </div>
                           </div>
                        )}

                     {/* Approve/Disapprove buttons for pending documents */}
                     {document.driverKyc.status === 'PENDING' && (
                        <div className='pt-4 border-t border-gray-200'>
                           <div className='flex items-center justify-end gap-3'>
                              <Button
                                 variant='outline'
                                 size='sm'
                                 onClick={() => setApprovingDocument({ id: document.driverKyc!.id, name: document.name })}
                                 className='gap-2 text-green-600 hover:text-green-700 border-green-200 hover:border-green-300'
                              >
                                 <Check className='w-4 h-4' />
                                 Approve
                              </Button>
                              <Button
                                 variant='outline'
                                 size='sm'
                                 onClick={() => setRejectingDocument({ id: document.driverKyc!.id, name: document.name })}
                                 className='gap-2 text-red-600 hover:text-red-700 border-red-200 hover:border-red-300'
                              >
                                 <X className='w-4 h-4' />
                                 Reject
                              </Button>
                           </div>
                        </div>
                     )}

                     {/* Re-upload option for rejected documents */}
                     {document.driverKyc.status === 'REJECTED' && (
                        <div className='pt-4 border-t border-gray-200'>
                           <div className='flex justify-end'>
                              <Button
                                 variant='outline'
                                 size='sm'
                                 onClick={() => setUploadingDocumentId(document.id)}
                                 className='gap-2'
                              >
                                 <Upload className='w-4 h-4' />
                                 Re-upload Document
                              </Button>
                           </div>
                        </div>
                     )}
                  </div>
               ) : (
                  /* Upload Section */
                  <div className='text-center py-8 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50'>
                     <div className='w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center mx-auto mb-4'>
                        <FileText className='w-6 h-6 text-gray-400' />
                     </div>
                     <h4 className='text-sm font-medium text-gray-900 mb-2'>
                        No document uploaded
                     </h4>
                     <p className='text-xs text-gray-500 mb-4'>
                        Upload your {document.name.toLowerCase()} to complete verification
                     </p>
                     <Button
                        variant='outline'
                        size='sm'
                        onClick={() => setUploadingDocumentId(document.id)}
                        className='gap-2'
                     >
                        <Upload className='w-4 h-4' />
                        Upload Document
                     </Button>
                  </div>
               )}

               {/* Upload Modal */}
               <KycDocumentUpload
                  document={document}
                  driverId={driverId}
                  open={uploadingDocumentId === document.id}
                  onClose={() => setUploadingDocumentId(null)}
                  editMode={document.driverKyc?.status === 'REJECTED'} // Use edit mode for rejected documents
                  onSuccess={() => {
                     setUploadingDocumentId(null);
                     queryClient.invalidateQueries({
                        queryKey: ['driver-kyc-documents', driverId],
                     });
                  }}
               />
            </div>
         ))}

         {/* Approve KYC Modal */}
         {approvingDocument && (
            <ApproveKycModal
               open={approvingDocument !== null}
               onClose={() => setApprovingDocument(null)}
               onSuccess={() => queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] })}
               documentId={approvingDocument.id}
               documentName={approvingDocument.name}
            />
         )}

         {/* Reject KYC Modal */}
         {rejectingDocument && (
            <RejectKycModal
               open={rejectingDocument !== null}
               onClose={() => setRejectingDocument(null)}
               onSuccess={() => queryClient.invalidateQueries({ queryKey: ['driver-kyc-documents', driverId] })}
               documentId={rejectingDocument.id}
               documentName={rejectingDocument.name}
            />
         )}
      </div>
   );
}
