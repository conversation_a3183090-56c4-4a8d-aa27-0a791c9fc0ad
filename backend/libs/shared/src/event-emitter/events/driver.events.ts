import { DriverProfileDto } from "../dto/driver-profile.dto";
import { IEventName } from "../interface/events.enum";

export class DRIVER_CREATE_EVENT {
    name !: IEventName.DRIVER_CREATED;
    data !: DriverProfileDto;
}

export class DRIVER_UPDATE_EVENT {
    name !: IEventName.DRIVER_UPDATED;
    data !: DriverProfileDto;
}

export class DRIVER_DELETE_EVENT {
    name !: IEventName.DRIVER_DELETED;
    data !: DriverProfileDto;
}

export class DRIVER_VEHICLE_CREATE_EVENT {
    name !: IEventName.DRIVER_VEHICLE_CREATED;
    data !: DriverProfileDto;
}

export class DRIVER_VEHICLE_UPDATE_EVENT {
    name !: IEventName.DRIVER_VEHICLE_UPDATED;
    data !: DriverProfileDto;
}