import { Module } from '@nestjs/common';
import { ZoneService } from './zone.service';
import { ZoneRepository } from '../../repositories/zone.repository';
import { H3IndexToZoneRepository } from '../../repositories/h3IndexToZone.repository';
import { PrismaService } from '../../database/prisma/prisma.service';

@Module({
  providers: [ZoneService, ZoneRepository, H3IndexToZoneRepository, PrismaService],
  exports: [ZoneService, ZoneRepository, H3IndexToZoneRepository],
})
export class ZoneModule {}
