import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import {
  City,
  CityStatus,
} from '@shared/shared/repositories/models/city.model';
import { PaginationDto } from 'apps/api/src/common/dto/pagination.dto';
import { FileUploadService } from '@shared/shared/common/file-upload/aws-s3/aws-file-upload.servie';
import { CityRepository } from '@shared/shared/repositories/city.repository';

@Injectable()
export class CityService {
  constructor(
    private readonly cityRepository: CityRepository,
    private readonly fileUploadService: FileUploadService,
  ) { }

  async createCity(
    data: Omit<
      City,
      'id' | 'createdAt' | 'updatedAt' | 'deletedAt' | 'status'
    > & { status?: CityStatus },
  ): Promise<City> {
    // Check for duplicate name and state combination
    const existingCity = await this.cityRepository.findCityByNameAndState(
      data.name,
      data.state || undefined,
    );
    if (existingCity) {
      throw new BadRequestException(
        `City with name "${data.name}" already exists in state "${data.state || 'N/A'}"`,
      );
    }

    const city = await this.cityRepository.createCity({
      ...data,
      status: data.status || CityStatus.ACTIVE,
    });

    return this.addSignedUrlToCity(city);
  }

  async findAllCities(): Promise<City[]> {
    const cities = await this.cityRepository.findAllCities();
    return this.addSignedUrlsToCities(cities);
  }

  async findCityById(id: string): Promise<City> {
    const city = await this.cityRepository.findCityById(id);
    if (!city) throw new NotFoundException(`City with ID ${id} not found`);
    return this.addSignedUrlToCity(city);
  }

  async updateCity(id: string, data: Partial<City>): Promise<City> {
    // Check if city exists
    const existingCity = await this.cityRepository.findCityById(id);
    if (!existingCity) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    // Check for duplicate name and state combination (excluding current record)
    if (data.name || data.state !== undefined) {
      const nameToCheck = data.name || existingCity.name;
      const stateToCheck =
        data.state !== undefined ? data.state : existingCity.state;

      const duplicateCity = await this.cityRepository.findCityByNameAndState(
        nameToCheck,
        stateToCheck || undefined,
      );
      if (duplicateCity && duplicateCity.id !== id) {
        throw new BadRequestException(
          `City with name "${nameToCheck}" already exists in state "${stateToCheck || 'N/A'}"`,
        );
      }
    }

    // Handle icon deletion if icon is being updated and old icon exists
    if (
      data.icon !== undefined &&
      existingCity.icon &&
      data.icon !== existingCity.icon
    ) {
      try {
        await this.fileUploadService.deleteFile(existingCity.icon);
      } catch (error) {
        console.warn(
          `Failed to delete old icon file: ${existingCity.icon}`,
          error,
        );
      }
    }

    const updatedCity = await this.cityRepository.updateCity(id, data);
    return this.addSignedUrlToCity(updatedCity);
  }

  async deleteCity(id: string): Promise<City> {
    return this.cityRepository.deleteCity(id);
  }

  async paginateCities(
    page = 1,
    limit = 10,
    dto?: PaginationDto & { state?: string; status?: string },
  ) {
    const options = this.buildPaginateOptions(dto);
    const result = await this.cityRepository.paginateCities(
      page,
      limit,
      options,
    );

    return {
      ...result,
      data: await this.addSignedUrlsToCities(result.data),
    };
  }

  /**
   * Paginate cities for admin with additional filters
   */
  async paginateCitiesForAdmin(
    page = 1,
    limit = 10,
    dto?: PaginationDto & { state?: string; status?: string },
  ) {
    return this.paginateCities(page, limit, dto);
  }

  /**
   * Change city status (active/inactive)
   */
  async changeCityStatus(id: string, status: CityStatus): Promise<City> {
    const city = await this.cityRepository.findCityById(id);
    if (!city) {
      throw new NotFoundException(`City with ID ${id} not found`);
    }

    const updatedCity = await this.cityRepository.updateCityStatus(id, status);
    return this.addSignedUrlToCity(updatedCity);
  }

  /**
   * Build options for pagination, supporting search by name, state filter, and status filter
   */
  private buildPaginateOptions(
    dto?: PaginationDto & { state?: string; status?: string },
  ) {
    const options: any = {};
    if (dto) {
      const whereConditions: any = {};

      // Search by name
      if (dto.search) {
        whereConditions.name = {
          contains: dto.search,
          mode: 'insensitive',
        };
      }

      // Filter by state
      if (dto.state) {
        whereConditions.state = {
          contains: dto.state,
          mode: 'insensitive',
        };
      }

      // Filter by status
      if (dto.status) {
        whereConditions.status = dto.status;
      }

      if (Object.keys(whereConditions).length > 0) {
        options.where = whereConditions;
      }

      // Sorting
      if (dto.sortBy) {
        options.orderBy = { [dto.sortBy]: dto.sortOrder || 'asc' };
      }
    }
    return options;
  }

  /**
   * Helper method to add signed URL to single city
   */
  private async addSignedUrlToCity(city: City): Promise<City> {
    if (city.icon) {
      try {
        const signedUrl = await this.fileUploadService.getSignedUrl(
          city.icon,
          3600, // 1 hour expiry
        );
        return { ...city, icon: signedUrl };
      } catch (error) {
        // If signed URL generation fails, keep the original URL
        return city;
      }
    }
    return city;
  }

  /**
   * Helper method to add signed URLs to array of cities
   */
  private async addSignedUrlsToCities(cities: City[]): Promise<City[]> {
    return Promise.all(
      cities.map(async (city) => {
        return this.addSignedUrlToCity(city);
      }),
    );
  }
}
