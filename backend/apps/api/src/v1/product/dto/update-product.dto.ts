import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsOptional,
  IsUUID,
  MaxLength,
  IsBoolean,
} from 'class-validator';

export class UpdateProductDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Vehicle type ID that this product belongs to',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Vehicle type ID must be a valid UUID' })
  vehicleTypeId?: string;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Product service ID (optional)',
    required: false,
  })
  @IsOptional()
  @IsUUID(4, { message: 'Product service ID must be a valid UUID' })
  productServiceId?: string;

  @ApiProperty({
    example: 'Premium Ride',
    description: 'Name of the product',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Product name must be a string' })
  @MaxLength(255, { message: 'Product name cannot exceed 255 characters' })
  name?: string;

  @ApiProperty({
    example: 'Luxury and comfortable ride with premium amenities',
    description: 'Description of the product',
    maxLength: 1000,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Product description must be a string' })
  @MaxLength(1000, {
    message: 'Product description cannot exceed 1000 characters',
  })
  description?: string;

  @ApiProperty({
    example: 'uploads/products/premium-ride-icon.png',
    description: 'Icon file path for the product',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Product icon must be a string' })
  icon?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the product is enabled',
    required: false,
  })
  @IsOptional()
  @IsBoolean({ message: 'isEnabled must be a boolean' })
  isEnabled?: boolean;
}
