import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { PrismaService } from '../database/prisma/prisma.service';
import { City, CityStatus } from './models/city.model';

@Injectable()
export class CityRepository extends BaseRepository<City> {
  protected readonly modelName = 'city';

  constructor(prisma: PrismaService) {
    super(prisma);
  }

  async createCity(
    data: Omit<City, 'id' | 'createdAt' | 'updatedAt' | 'deletedAt'>,
  ): Promise<City> {
    return this.create(data, {
      include: {
        userProfiles: true,
        cityProducts: true,
      },
    });
  }

  async findAllCities(): Promise<City[]> {
    return this.findMany({
      include: {
        userProfiles: true,
        cityProducts: true,
      },
    });
  }

  async findCityById(id: string): Promise<City | null> {
    return this.findById(id, {
      include: {
        userProfiles: true,
        cityProducts: true,
      },
    });
  }

  async updateCity(id: string, data: Partial<City>): Promise<City> {
    return this.updateById(id, data);
  }

  async deleteCity(id: string): Promise<City> {
    return this.softDeleteById(id);
  }

  async paginateCities(page = 1, limit = 10, options?: any) {
    return this.paginate(page, limit, {
      include: {
        userProfiles: true,
        cityProducts: true,
      },
      ...options,
    });
  }

  /**
   * Find city by name and state for uniqueness check
   */
  async findCityByNameAndState(
    name: string,
    state?: string,
  ): Promise<City | null> {
    return this.findOne({
      where: {
        name: {
          equals: name,
          mode: 'insensitive',
        },
        state: state || null,
      },
    });
  }

  /**
   * Find cities by H3 index
   */
  async findCitiesByH3Index(h3Index: bigint): Promise<City[]> {
    return this.findMany({
      where: {
        h3Indexes: {
          has: h3Index,
        },
      },
      include: {
        userProfiles: true,
        cityProducts: true,
      },
    });
  }

  /**
   * Update city status
   */
  async updateCityStatus(id: string, status: CityStatus): Promise<City> {
    return this.updateById(
      id,
      { status },
      {
        include: {
          userProfiles: true,
          cityProducts: true,
        },
      },
    );
  }
}
