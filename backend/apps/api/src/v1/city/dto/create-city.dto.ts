import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';

export enum CityStatusDto {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export class CreateCityDto {
  @ApiProperty({ example: 'Kochi' })
  @IsString()
  name!: string;

  @ApiProperty({
    example: 'uploads/cities/kochi-icon.png',
    description: 'Icon file path for the city',
    required: false,
  })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({
    example: 'Kerala',
    description: 'State where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiProperty({
    example: 'India',
    description: 'Country where the city is located',
    required: false,
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiProperty({
    example: 'active',
    enum: CityStatusDto,
    description: 'Status of the city',
    default: 'active',
    required: false,
  })
  @IsOptional()
  @IsEnum(CityStatusDto)
  status?: CityStatusDto;
}
