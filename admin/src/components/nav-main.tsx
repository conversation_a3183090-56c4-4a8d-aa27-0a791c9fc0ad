"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
   SidebarGroup,
   SidebarMenu,
   SidebarMenuButton,
   SidebarMenuItem,
   SidebarMenuSub,
   SidebarMenuSubButton,
   SidebarMenuSubItem,
} from '@/components/ui/sidebar';

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
    }[]
  }[]
}) {
  return (
     <SidebarGroup className='py-2'>
        <SidebarMenu className='space-y-1'>
           {items.map(item => {
              // If item has sub-items, render as collapsible
              if (item.items && item.items.length > 0) {
                 return (
                    <Collapsible
                       key={item.title}
                       asChild
                       defaultOpen={item.isActive}
                       className='group/collapsible'
                    >
                       <SidebarMenuItem>
                          <CollapsibleTrigger asChild>
                             <SidebarMenuButton 
                                tooltip={item.title} 
                                className='h-8 px-3 py-1 text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md font-medium'
                             >
                                {item.icon && <item.icon className='w-4 h-4' />}
                                <span className='text-sm'>{item.title}</span>
                                <ChevronRight className='ml-auto w-3 h-3 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90' />
                             </SidebarMenuButton>
                          </CollapsibleTrigger>
                          <CollapsibleContent>
                             <SidebarMenuSub>
                                {item.items.map(subItem => (
                                   <SidebarMenuSubItem key={subItem.title}>
                                      <SidebarMenuSubButton asChild>
                                         <a href={subItem.url}>
                                            <span>{subItem.title}</span>
                                         </a>
                                      </SidebarMenuSubButton>
                                   </SidebarMenuSubItem>
                                ))}
                             </SidebarMenuSub>
                          </CollapsibleContent>
                       </SidebarMenuItem>
                    </Collapsible>
                 );
              }

              // If no sub-items, render as direct link
              return (
                 <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton 
                       asChild 
                       tooltip={item.title} 
                       isActive={item.isActive}
                       className={`h-8 px-3 py-1 rounded-md font-medium transition-colors ${
                          item.isActive 
                             ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                             : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                       }`}
                    >
                       <a href={item.url} className='flex items-center gap-2 w-full'>
                          {item.icon && <item.icon className='w-4 h-4' />}
                          <span className='text-sm'>{item.title}</span>
                       </a>
                    </SidebarMenuButton>
                 </SidebarMenuItem>
              );
           })}
        </SidebarMenu>
     </SidebarGroup>
  );
}
