import { ApiProperty } from '@nestjs/swagger';
import {
  IsString,
  IsNotEmpty,
  IsUUID,
  MaxLength,
  IsOptional,
  IsBoolean,
} from 'class-validator';

export class CreateProductDto {
  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Vehicle type ID that this product belongs to',
  })
  @IsUUID(4, { message: 'Vehicle type ID must be a valid UUID' })
  @IsNotEmpty({ message: 'Vehicle type ID is required' })
  vehicleTypeId!: string;

  @ApiProperty({
    example: 'c1a2b3c4-d5e6-7890-abcd-ef1234567890',
    description: 'Product service ID (optional)',
    required: false,
  })
  @IsUUID(4, { message: 'Product service ID must be a valid UUID' })
  @IsOptional()
  productServiceId?: string;

  @ApiProperty({
    example: 'Standard Ride',
    description: 'Name of the product',
    maxLength: 255,
  })
  @IsString({ message: 'Product name must be a string' })
  @IsNotEmpty({ message: 'Product name is required' })
  @MaxLength(255, { message: 'Product name cannot exceed 255 characters' })
  name!: string;

  @ApiProperty({
    example: 'Comfortable and affordable ride for daily commuting',
    description: 'Description of the product',
    maxLength: 1000,
    required: false,
  })
  @IsString({ message: 'Product description must be a string' })
  @IsOptional()
  @MaxLength(1000, {
    message: 'Product description cannot exceed 1000 characters',
  })
  description?: string;

  @ApiProperty({
    example: 'standard_ride',
    description:
      'Unique identifier for the product (auto-generated if not provided)',
    required: false,
  })
  @IsString({ message: 'Product identifier must be a string' })
  @IsOptional()
  @MaxLength(255, {
    message: 'Product identifier cannot exceed 255 characters',
  })
  identifier?: string;

  @ApiProperty({
    example: 'uploads/products/standard-ride-icon.png',
    description: 'Icon file path for the product',
    required: false,
  })
  @IsString({ message: 'Product icon must be a string' })
  @IsOptional()
  icon?: string;

  @ApiProperty({
    example: true,
    description: 'Whether the product is enabled',
    default: true,
    required: false,
  })
  @IsBoolean({ message: 'isEnabled must be a boolean' })
  @IsOptional()
  isEnabled?: boolean;
}
